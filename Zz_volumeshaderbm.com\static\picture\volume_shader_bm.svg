<?xml version="1.0" encoding="UTF-8"?>
<svg width="1024" height="1024" viewBox="0 0 1024 1024" xmlns="http://www.w3.org/2000/svg">
    <defs>
        <linearGradient id="grad1" x1="0%" y1="0%" x2="100%" y2="100%">
            <stop offset="0%" style="stop-color:#6366f1;stop-opacity:1" />
            <stop offset="100%" style="stop-color:#8b5cf6;stop-opacity:1" />
        </linearGradient>
    </defs>
    <rect width="1024" height="1024" fill="url(#grad1)" opacity="0.1"/>
    <g transform="translate(512,512)">
        <path d="M-200,-200 L200,-200 L200,200 L-200,200 Z" fill="url(#grad1)" opacity="0.2"/>
        <path d="M-200,-200 L-200,200 L200,200 L200,-200 Z" fill="url(#grad1)" opacity="0.3"/>
        
        <g stroke="#ffffff" stroke-width="4" fill="none">
            <path d="M-150,-150 L-150,150" opacity="0.6"/>
            <path d="M0,-150 L0,150" opacity="0.8"/>
            <path d="M150,-150 L150,150" opacity="0.6"/>
            
            <path d="M-150,-150 L150,-150" opacity="0.6"/>
            <path d="M-150,0 L150,0" opacity="0.8"/>
            <path d="M-150,150 L150,150" opacity="0.6"/>
            
            <path d="M-150,-150 L150,150" opacity="0.4"/>
            <path d="M150,-150 L-150,150" opacity="0.4"/>
        </g>
        
        <circle cx="0" cy="0" r="225" fill="url(#grad1)" opacity="0.1"/>
        <g stroke="#ffffff" stroke-width="2" fill="none" opacity="0.3">
            <path d="M-100,-100 L100,-100 L100,100 L-100,100 Z"/>
            <path d="M-100,-100 L-100,100 L100,100 L100,-100 Z"/>
        </g>
    </g>
</svg> 