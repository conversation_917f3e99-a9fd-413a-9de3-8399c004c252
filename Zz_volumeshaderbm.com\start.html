﻿<!DOCTYPE html>
<html lang="en" dir="ltr">
<head>
    <script>
        if (window.location.hostname.includes(".pages.dev")) {
            const newUrl =
                "https://volumeshaderbm.com" + window.location.pathname + window.location.search;
            window.location.replace(newUrl);
        }
    </script>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Volume Shader BM GPU Benchmark Test - Advanced 3D Shader Performance</title>
    <meta name="description" content="Test your device&#x27;s GPU performance with Volume Shader BM&#x27;s advanced 3D shader benchmark. Analyze rendering speed, detect lag, and optimize your graphics capabilities.">
    <meta name="author" content="Hamid Ali">
    <meta property="og:type" content="website">
    <meta property="og:title" content="Volume Shader BM GPU Benchmark Test - Advanced 3D Shader Performance">
    <meta property="og:url" content="https://volumeshaderbm.com/start/">
    <meta property="og:site_name" content="Volume Shader BM">
    <meta property="og:image" content="https://volumeshaderbm.com/volume_shader_bm.svg">
    <meta property="og:description" content="Test your device&#x27;s GPU performance with Volume Shader BM&#x27;s advanced 3D shader benchmark. Analyze rendering speed, detect lag, and optimize your graphics capabilities.">
    <meta property="og:locale" content="en_US">
    <meta name="twitter:card" content="summary">
    <meta name="twitter:title" content="Volume Shader BM GPU Benchmark Test - Advanced 3D Shader Performance">
    <meta name="twitter:description" content="Test your device&#x27;s GPU performance with Volume Shader BM&#x27;s advanced 3D shader benchmark. Analyze rendering speed, detect lag, and optimize your graphics capabilities.">
    <meta name="twitter:creator" content="Volume Shader BM">
    <meta name="twitter:image" content="https://volumeshaderbm.com/volume_shader_bm.svg">
    <link rel="apple-touch-icon" sizes="57x57" href="https://volumeshaderbm.com/apple-icon-57x57.png">
    <link rel="apple-touch-icon" sizes="60x60" href="https://volumeshaderbm.com/apple-icon-60x60.png">
    <link rel="apple-touch-icon" sizes="72x72" href="https://volumeshaderbm.com/apple-icon-72x72.png">
    <link rel="apple-touch-icon" sizes="76x76" href="https://volumeshaderbm.com/apple-icon-76x76.png">
    <link rel="apple-touch-icon" sizes="114x114" href="https://volumeshaderbm.com/apple-icon-114x114.png">
    <link rel="apple-touch-icon" sizes="120x120" href="https://volumeshaderbm.com/apple-icon-120x120.png">
    <link rel="apple-touch-icon" sizes="144x144" href="https://volumeshaderbm.com/apple-icon-144x144.png">
    <link rel="apple-touch-icon" sizes="152x152" href="https://volumeshaderbm.com/apple-icon-152x152.png">
    <link rel="apple-touch-icon" sizes="180x180" href="https://volumeshaderbm.com/apple-icon-180x180.png">
    <link rel="icon" type="image/png" sizes="192x192" href="https://volumeshaderbm.com/android-icon-192x192.png">
    <link rel="icon" type="image/png" sizes="32x32" href="https://volumeshaderbm.com/favicon-32x32.png">
    <link rel="icon" type="image/png" sizes="96x96" href="https://volumeshaderbm.com/favicon-96x96.png">
    <link rel="icon" type="image/png" sizes="16x16" href="https://volumeshaderbm.com/favicon-16x16.png">
    <meta name="msapplication-TileColor" content="#ffffff">
    <meta name="msapplication-TileImage" content="https://volumeshaderbm.com/ms-icon-144x144.png">
    <meta name="theme-color" content="#ffffff">
    <link rel="manifest" href="https://volumeshaderbm.com/manifest.json">
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link rel="stylesheet" href="static/css/css2.css" media="print" onload="this.media='all'">
    <noscript>
        <link href="static/css/css2.css" rel="stylesheet" type="text/css">
    </noscript>
    <script async src="https://pagead2.googlesyndication.com/pagead/js/adsbygoogle.js?client=ca-pub-9497489198152256" crossorigin="anonymous"></script>
<style>
    body {
        margin: 0;
        overflow: hidden;
        background: #000;
    }

    canvas {
        display: block;
        width: 100%;
        height: 100vh;
        touch-action: none;
    }
</style>  <script type="module" crossorigin src="static/js/app.BdJpEPPz.js"></script>
  <link rel="stylesheet" crossorigin href="static/css/app.6vf3RASq.css">
</head>
<body><div id="performancePanel" class="fixed top-2 left-2 bg-black/80 text-white p-3 rounded-lg backdrop-blur-sm z-50 font-karla text-sm hidden">
    <div>FPS: <span id="fps">0</span></div>
    <div>Status: <span id="status">Initializing...</span></div>
</div>

<div class="fixed inset-0 bg-black/50 items-center justify-center z-[1000] hidden" id="loadingOverlay">
    <div class="w-10 h-10 border-4 border-gray-200 border-t-blue-500 rounded-full animate-spin"></div>
</div>

<div id="errorMessage" class="fixed bottom-4 left-1/2 transform -translate-x-1/2 bg-red-500 text-white px-6 py-3 rounded-lg hidden z-[100] animate-fade-in"></div>

<div class="fixed top-2 right-2 z-50 flex flex-col items-end justify-end gap-2">
    <div class="flex gap-2">
        <a href="index.html" class="text-base bg-green-50 hover:bg-green-100 text-green-700 px-2 py-1 rounded-lg backdrop-blur-sm transition-colors">
            Home
        </a>
        <button id="infoButton" class="text-base bg-green-50 hover:bg-green-100 text-green-700 px-2 py-1 rounded-lg backdrop-blur-sm transition-colors">
            Info
        </button>
        <button id="btn" class="text-base text-red-700 bg-red-50 border border-red-200 px-2 py-1 rounded-lg hover:bg-red-100 transition-colors z-50">
            Config
        </button>
    </div>
      <div class="w-full flex flex-wrap justify-center items-center gap-1.5">
        <a href="https://www.linkedin.com/sharing/share-offsite?mini=true&amp;url=https://volumeshaderbm.com&amp;title=Free+Volume+Shader+BM+Test&amp;summary=Free+Volume+Shader+BM+Test" id="linkedin-share-button" title="Share this on LinkedIn" target="_blank" rel="noopener noreferrer">
           <span class="sr-only">Share this on LinkedIn</span>
          <svg xmlns="http://www.w3.org/2000/svg" viewbox="0 0 92 92" fill="none" width="32" height="32">
            <rect x="0.138672" y="1" width="91.5618" height="91.5618" rx="15" fill="#fff"></rect>
            <path fill-rule="evenodd" clip-rule="evenodd" d="M24.6975 21.5618C22.6561 21.5618 21 23.1674 21 25.1456V68.0091C21 69.9875 22.6563 71.5918 24.6975 71.5918H67.3325C69.3747 71.5918 71.03 69.9873 71.03 68.0086V25.1456C71.03 23.1674 69.3747 21.5618 67.3325 21.5618H24.6975ZM36.2032 40.9068V63.4304H28.7167V40.9068H36.2032ZM36.6967 33.9411C36.6967 36.1025 35.0717 37.8321 32.4615 37.8321L32.4609 37.8319H32.4124C29.8998 37.8319 28.2754 36.1023 28.2754 33.9409C28.2754 31.7304 29.9489 30.0491 32.5111 30.0491C35.0717 30.0491 36.6478 31.7304 36.6967 33.9411ZM47.833 63.4304H40.3471L40.3469 63.4312C40.3469 63.4312 40.4452 43.0205 40.3475 40.9075H47.8336V44.0957C48.8288 42.5613 50.6098 40.3787 54.5808 40.3787C59.5062 40.3787 63.1991 43.598 63.1991 50.516V63.4304H55.7133V51.3822C55.7133 48.354 54.6293 46.2887 51.921 46.2887C49.8524 46.2887 48.6206 47.6815 48.0796 49.0271C47.8819 49.5072 47.833 50.1813 47.833 50.8535V63.4304Z" fill="#006699"></path>
          </svg>
        </a>
    
        <a href="https://www.facebook.com/sharer/sharer.php?u=https://volumeshaderbm.com" id="facebook-share-button" title="Share this on Facebook" target="_blank" rel="noopener noreferrer">
           <span class="sr-only">Share this on Facebook</span>
          <svg xmlns="http://www.w3.org/2000/svg" viewbox="0 0 92 92" fill="none" width="32" height="32">
            <rect x="1.13867" width="91.5618" height="91.5618" rx="15" fill="#fff"></rect>
            <path d="M56.4927 48.6403L57.7973 40.3588H49.7611V34.9759C49.7611 32.7114 50.883 30.4987 54.4706 30.4987H58.1756V23.4465C56.018 23.1028 53.8378 22.9168 51.6527 22.8901C45.0385 22.8901 40.7204 26.8626 40.7204 34.0442V40.3588H33.3887V48.6403H40.7204V68.671H49.7611V48.6403H56.4927Z" fill="#337FFF"></path>
          </svg>
        </a>
    
        <a href="https://x.com/intent/post?text=Free+Volume+Shader+BM+Test&amp;url=https://volumeshaderbm.com" id="x-share-button" title="Share this on X" target="_blank" rel="noopener noreferrer">
           <span class="sr-only">Share this on X</span>
          <svg xmlns="http://www.w3.org/2000/svg" viewbox="0 0 92 92" fill="none" width="32" height="32">
            <rect x="0.138672" width="91.5618" height="91.5618" rx="15" fill="#fff"></rect>
            <path d="M50.7568 42.1716L69.3704 21H64.9596L48.7974 39.383L35.8887 21H21L40.5205 48.7983L21 71H25.4111L42.4788 51.5869L56.1113 71H71L50.7557 42.1716H50.7568ZM44.7152 49.0433L42.7374 46.2752L27.0005 24.2492H33.7756L46.4755 42.0249L48.4533 44.7929L64.9617 67.8986H58.1865L44.7152 49.0443V49.0433Z" fill="#000"></path>
          </svg>
        </a>
    
        <a href="https://wa.me/?text=https://volumeshaderbm.com" id="whatsapp-share-button" title="Share this on WhatsApp" target="_blank" rel="noopener noreferrer">
           <span class="sr-only">Share this on WhatsApp</span>
          <svg xmlns="http://www.w3.org/2000/svg" viewbox="0 0 92 92" fill="none" width="32" height="32">
            <rect x="1.13867" width="91.5618" height="91.5618" rx="15" fill="#fff"></rect>
            <path d="M23.5762 66.8405L26.8608 54.6381C24.2118 49.8847 23.3702 44.3378 24.4904 39.0154C25.6106 33.693 28.6176 28.952 32.9594 25.6624C37.3012 22.3729 42.6867 20.7554 48.1276 21.1068C53.5685 21.4582 58.6999 23.755 62.5802 27.5756C66.4604 31.3962 68.8292 36.4844 69.2519 41.9065C69.6746 47.3286 68.1228 52.7208 64.8813 57.0938C61.6399 61.4668 56.9261 64.5271 51.605 65.7133C46.284 66.8994 40.7125 66.1318 35.9131 63.5513L23.5762 66.8405ZM36.508 58.985L37.2709 59.4365C40.7473 61.4918 44.8076 62.3423 48.8191 61.8555C52.8306 61.3687 56.5681 59.5719 59.4489 56.7452C62.3298 53.9185 64.1923 50.2206 64.7463 46.2279C65.3002 42.2351 64.5143 38.1717 62.5113 34.6709C60.5082 31.1701 57.4003 28.4285 53.6721 26.8734C49.9438 25.3184 45.8045 25.0372 41.8993 26.0736C37.994 27.11 34.5422 29.4059 32.0817 32.6035C29.6212 35.801 28.2903 39.7206 28.2963 43.7514C28.293 47.0937 29.2197 50.3712 30.9732 53.2192L31.4516 54.0061L29.6153 60.8167L36.508 58.985Z" fill="#00D95F"></path>
            <path fill-rule="evenodd" clip-rule="evenodd" d="M55.0259 46.8847C54.5787 46.5249 54.0549 46.2716 53.4947 46.1442C52.9344 46.0168 52.3524 46.0186 51.793 46.1495C50.9524 46.4977 50.4093 47.8134 49.8661 48.4713C49.7516 48.629 49.5833 48.7396 49.3928 48.7823C49.2024 48.8251 49.0028 48.797 48.8316 48.7034C45.7543 47.5012 43.1748 45.2965 41.5122 42.4475C41.3704 42.2697 41.3033 42.044 41.325 41.8178C41.3467 41.5916 41.4555 41.3827 41.6286 41.235C42.2344 40.6368 42.6791 39.8959 42.9218 39.0809C42.9756 38.1818 42.7691 37.2863 42.3269 36.5011C41.985 35.4002 41.3344 34.42 40.4518 33.6762C39.9966 33.472 39.4919 33.4036 38.9985 33.4791C38.5052 33.5546 38.0443 33.7709 37.6715 34.1019C37.0242 34.6589 36.5104 35.3537 36.168 36.135C35.8256 36.9163 35.6632 37.7643 35.6929 38.6165C35.6949 39.0951 35.7557 39.5716 35.8739 40.0354C36.1742 41.1497 36.636 42.2144 37.2447 43.1956C37.6839 43.9473 38.163 44.6749 38.6801 45.3755C40.3607 47.6767 42.4732 49.6305 44.9003 51.1284C46.1183 51.8897 47.42 52.5086 48.7799 52.973C50.1924 53.6117 51.752 53.8568 53.2931 53.6824C54.1711 53.5499 55.003 53.2041 55.7156 52.6755C56.4281 52.1469 56.9995 51.4518 57.3795 50.6512C57.6028 50.1675 57.6705 49.6269 57.5735 49.1033C57.3407 48.0327 55.9053 47.4007 55.0259 46.8847Z" fill="#00D95F"></path>
          </svg>
        </a>
    
        <a href="https://t.me/share/url?url=https://volumeshaderbm.com&amp;text=Free+Volume+Shader+BM+Test" id="telegram-share-button" title="Share this on Telegram" target="_blank" rel="noopener noreferrer">
           <span class="sr-only">Share this on Telegram</span>
          <svg xmlns="http://www.w3.org/2000/svg" viewbox="0 0 92 92" fill="none" width="32" height="32">
            <rect x="0.138672" y="1" width="91.5618" height="91.5618" rx="15" fill="#fff"></rect>
            <path fill-rule="evenodd" clip-rule="evenodd" d="M71.153 49.5768C71.153 63.3922 59.9534 74.5918 46.138 74.5918C32.3227 74.5918 21.123 63.3922 21.123 49.5768C21.123 35.7614 32.3227 24.5618 46.138 24.5618C59.9534 24.5618 71.153 35.7614 71.153 49.5768ZM48.5352 41.901C44.2377 43.7573 31.8466 49.0289 31.8466 49.0289C28.91 50.2169 30.6289 51.3306 30.6289 51.3306C30.6289 51.3306 33.1357 52.2216 35.2846 52.8898C37.4333 53.558 38.5793 52.8156 38.5793 52.8156C38.5793 52.8156 43.5931 49.4002 48.6784 45.7619C52.2597 43.2375 51.4002 45.3165 50.5407 46.2075C48.6784 48.138 45.5986 51.1821 43.02 53.6323C41.8741 54.6719 42.4471 55.5628 42.9485 56.0083C44.4067 57.2873 47.8411 59.6135 49.4003 60.6696C49.8324 60.9623 50.1204 61.1574 50.1826 61.2057C50.5407 61.5028 52.5463 62.8392 53.7639 62.5422C54.9815 62.2452 55.1247 60.5374 55.1247 60.5374C55.1247 60.5374 56.0558 54.4491 56.9154 48.8804C57.0746 47.787 57.2338 46.7166 57.3822 45.7184C57.768 43.1241 58.0812 41.0182 58.133 40.2675C58.3479 37.7431 55.7693 38.7825 55.7693 38.7825C55.7693 38.7825 50.1826 41.1586 48.5352 41.901Z" fill="#34AADF"></path>
          </svg>
        </a>
      </div>
</div>

<div id="config" class="fixed top-20 right-4 bg-white/95 p-4 rounded-lg shadow-lg z-50 max-w-[90%] w-[400px] hidden">
    <label class="block">
        <textarea id="kernel" class="w-full min-h-[200px] font-mono p-3 border border-gray-200 rounded bg-gray-50 resize-y focus:outline-none focus:ring-2 focus:ring-blue-500"></textarea>
    </label>
    <div class="flex gap-2 mt-3">
        <button id="apply" class="flex-1 bg-green-500 hover:bg-green-600 text-white px-2 py-1 rounded transition-colors">
            APPLY
        </button>
        <button id="undo" class="flex-1 bg-gray-100 hover:bg-gray-200 text-gray-700 px-2 py-1 rounded transition-colors">
            Undo
        </button>
    </div>
</div>

<div id="modal" class="fixed inset-0 bg-black/50 backdrop-blur-sm hidden z-50 overflow-y-auto">
    <div class="min-h-screen flex items-center justify-center p-4">
        <div class="bg-white rounded-lg p-6 max-w-2xl w-full mx-4 relative">
            <div class="flex justify-between items-center mb-4">
                <h2 class="text-xl font-bold text-primary">Volume Shader Benchmark Test</h2>
                <button id="closeModal" class="text-gray-500 hover:text-gray-700 text-sm">
                    Close
                </button>
            </div>
            <div class="space-y-4 max-h-[70vh] overflow-y-auto">
                <div>
                    <h3 class="text-lg font-semibold text-primary mb-2">What is being tested?</h3>
                    <p class="text-gray-700">
                        This advanced test evaluates your device&#x27;s GPU performance through sophisticated volume rendering:
                    </p>
                    <ul class="list-disc list-inside mt-2 text-gray-700">
                        <li>Real-time volume shader rendering</li>
                        <li>FPS monitoring and analysis</li>
                        <li>Lag detection and performance stability</li>
                        <li>3D visualization quality assessment</li>
                        <li>Cross-platform compatibility testing</li>
                        <li>Detailed performance metrics</li>
                    </ul>
                </div>
                <div>
                    <h3 class="text-lg font-semibold text-primary mb-2">Technical Specifications</h3>
                    <p class="text-gray-700">
                        The benchmark implements advanced volume rendering techniques:
                    </p>
                    <ul class="list-disc list-inside mt-2 text-gray-700">
                        <li>Advanced volume shader algorithms</li>
                        <li>Real-time FPS monitoring</li>
                        <li>Lag detection system</li>
                        <li>Cross-platform compatibility</li>
                        <li>Performance analytics dashboard</li>
                        <li>Social sharing integration</li>
                    </ul>
                </div>
                <div>
                    <h3 class="text-lg font-semibold text-primary mb-2">Interactive Controls</h3>
                    <ul class="list-disc list-inside mt-2 text-gray-700">
                        <li>Left Mouse Button: Rotate view</li>
                        <li>Right Mouse Button: Pan view</li>
                        <li>Mouse Wheel: Zoom in/out</li>
                        <li>Touch: Multi-touch gestures supported</li>
                        <li>Space: Reset view</li>
                        <li>R: Toggle performance panel</li>
                        <li>S: Share results</li>
                    </ul>
                </div>
            </div>
        </div>
    </div>
</div>

<canvas id="shader-container" width="1024" height="1024">
    <span>You browser does not support canvas...</span>
</canvas>

<div class="bg-white fixed bottom-1 max-w-xs mx-auto left-0 right-0 z-50 flex flex-col items-center justify-center gap-2 w-full" id="fixed-ad-container">
    <button class="absolute bg-black text-white rounded-full p-1 -top-3 -right-3 hover:text-gray-300" onclick="closeAd()">
        <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" viewbox="0 0 20 20" fill="currentColor">
            <path fill-rule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clip-rule="evenodd"></path>
        </svg>
    </button>
    <div>
        <script data-cfasync="false" async type="text/javascript" src="static/js/118159.js"></script>
    </div>
</div>

<script>
    function checkAdStatus() {
        const adClosedTime = localStorage.getItem('adClosedTime');
        if (adClosedTime) {
            const now = new Date().getTime();
            const timeDiff = now - parseInt(adClosedTime);
            const expireTime = 2 * 60 * 60 * 1000;

            if (timeDiff < expireTime) {
                document.getElementById('fixed-ad-container').style.display = 'none';
            }
        }
    }

    function closeAd() {
        const adElement = document.getElementById('fixed-ad-container');
        adElement.style.display = 'none';
        localStorage.setItem('adClosedTime', new Date().getTime());
    }

    document.addEventListener('DOMContentLoaded', checkAdStatus);
</script><script>
    var cx, cy;
    var glposition;
    var glright;
    var glforward;
    var glup;
    var glorigin;
    var glx;
    var gly;
    var gllen;
    var canvas;
    var gl;
    var date = new Date();
    var md = 0, mx, my;
    var t2, t1 = date.getTime();
    var mx = 0, my = 0, mx1 = 0, my1 = 0, lasttimen = 0;
    var ml = 0, mr = 0, mm = 0;
    var len = 1.6;
    var ang1 = 2.8;
    var ang2 = 0.4;
    var cenx = 0.0;
    var ceny = 0.0;
    var cenz = 0.0;
    var KERNEL = "float kernal(vec3 ver){\n" +
        "   vec3 a;\n" +
        "float b,c,d,e;\n" +
        "   a=ver;\n" +
        "   for(int i=0;i<5;i++){\n" +
        "       b=length(a);\n" +
        "       c=atan(a.y,a.x)*8.0;\n" +
        "       e=1.0/b;\n" +
        "       d=acos(a.z/b)*8.0;\n" +
        "       b=pow(b,8.0);\n" +
        "       a=vec3(b*sin(d)*cos(c),b*sin(d)*sin(c),b*cos(d))+ver;\n" +
        "       if(b>6.0){\n" +
        "           break;\n" +
        "       }\n" +
        "   }" +
        "   return 4.0-a.x*a.x-a.y*a.y-a.z*a.z;" +
        "}";
    var fragshader;
    var shaderProgram;

    let frameCount = 0;
    let lastFpsUpdate = 0;
    let fps = 0;

    function updatePerformance(currentTime) {
        frameCount++;
        if (currentTime - lastFpsUpdate >= 1000) {
            fps = Math.round((frameCount * 1000) / (currentTime - lastFpsUpdate));
            frameCount = 0;
            lastFpsUpdate = currentTime;
            updatePerformancePanel();
        }
    }

    function updatePerformancePanel() {
        const fpsElement = document.getElementById('fps');
        const statusElement = document.getElementById('status');
        const panel = document.getElementById('performancePanel');

        if (fpsElement && statusElement && panel) {
            fpsElement.textContent = fps;

            let status = 'Good';
            if (fps < 20) status = 'Poor';
            else if (fps < 30) status = 'Fair';

            statusElement.textContent = status;
            panel.classList.remove('hidden');
        }
    }

    function showError(message) {
        const errorElement = document.getElementById('errorMessage');
        if (errorElement) {
            errorElement.textContent = message;
            errorElement.classList.remove('hidden');
            setTimeout(() => {
                errorElement.classList.add('hidden');
            }, 5000);
        }
    }

    function showLoading() {
        const loading = document.getElementById('loadingOverlay');
        if (loading) loading.classList.remove('hidden');
    }

    function hideLoading() {
        const loading = document.getElementById('loadingOverlay');
        if (loading) loading.classList.add('hidden');
    }

    function ontimer() {
        const currentTime = performance.now();
        updatePerformance(currentTime);
        ang1 += 0.01;
        draw();
        window.requestAnimationFrame(ontimer);
    }

    document.addEventListener("mousedown",
        function (ev) {
            var oEvent = ev || event;
            if (oEvent.button == 0) {
                ml = 1;
                mm = 0;
            }
            if (oEvent.button == 2) {
                mr = 1;
                mm = 0;
            }
            mx = oEvent.clientX;
            my = oEvent.clientY;
        },
        false);
    document.addEventListener("mouseup",
        function (ev) {
            var oEvent = ev || event;
            if (oEvent.button == 0) {
                ml = 0;
            }
            if (oEvent.button == 2) {
                mr = 0;
            }
        },
        false);
    document.addEventListener("mousemove",
        function (ev) {
            var oEvent = ev || event;
            if (ml == 1) {
                ang1 += (oEvent.clientX - mx) * 0.002;
                ang2 += (oEvent.clientY - my) * 0.002;
                if (oEvent.clientX != mx || oEvent.clientY != my) {
                    mm = 1;
                }
            }
            if (mr == 1) {
                var l = len * 4.0 / (cx + cy);
                cenx += l * (-(oEvent.clientX - mx) * Math.sin(ang1) - (oEvent.clientY - my) * Math.sin(ang2) * Math.cos(ang1));
                ceny += l * ((oEvent.clientY - my) * Math.cos(ang2));
                cenz += l * ((oEvent.clientX - mx) * Math.cos(ang1) - (oEvent.clientY - my) * Math.sin(ang2) * Math.sin(ang1));
                if (oEvent.clientX != mx || oEvent.clientY != my) {
                    mm = 1;
                }
            }
            mx = oEvent.clientX;
            my = oEvent.clientY;
        },
        false);
    document.addEventListener("mousewheel",
        function (ev) {
            // ev.preventDefault();
            var oEvent = ev || event;
            len *= Math.exp(-0.001 * oEvent.wheelDelta);
        },
        false);
    document.addEventListener("touchstart",
        function (ev) {
            var n = ev.touches.length;
            if (n == 1) {
                var oEvent = ev.touches[0];
                mx = oEvent.clientX;
                my = oEvent.clientY;
            } else if (n == 2) {
                var oEvent = ev.touches[0];
                mx = oEvent.clientX;
                my = oEvent.clientY;
                oEvent = ev.touches[1];
                mx1 = oEvent.clientX;
                my1 = oEvent.clientY;
            }
            lasttimen = n;
        },
        false);
    document.addEventListener("touchend",
        function (ev) {
            var n = ev.touches.length;
            if (n == 1) {
                var oEvent = ev.touches[0];
                mx = oEvent.clientX;
                my = oEvent.clientY;
            } else if (n == 2) {
                var oEvent = ev.touches[0];
                mx = oEvent.clientX;
                my = oEvent.clientY;
                oEvent = ev.touches[1];
                mx1 = oEvent.clientX;
                my1 = oEvent.clientY;
            }
            lasttimen = n;
        },
        false);
    document.addEventListener("touchmove",
        function (ev) {
            // ev.preventDefault();
            var n = ev.touches.length;
            if (n == 1 && lasttimen == 1) {
                var oEvent = ev.touches[0];
                ang1 += (oEvent.clientX - mx) * 0.002;
                ang2 += (oEvent.clientY - my) * 0.002;
                mx = oEvent.clientX;
                my = oEvent.clientY;
            } else if (n == 2) {
                var oEvent = ev.touches[0];
                var oEvent1 = ev.touches[1];
                var l = len * 2.0 / (cx + cy), l1;
                cenx += l * (-(oEvent.clientX + oEvent1.clientX - mx - mx1) * Math.sin(ang1) - (oEvent.clientY + oEvent1.clientY - my - my1) * Math.sin(ang2) * Math.cos(ang1));
                ceny += l * ((oEvent.clientY + oEvent1.clientY - my - my1) * Math.cos(ang2));
                cenz += l * ((oEvent.clientX + oEvent1.clientX - mx - mx1) * Math.cos(ang1) - (oEvent.clientY + oEvent1.clientY - my - my1) * Math.sin(ang2) * Math.sin(ang1));
                l1 = Math.sqrt((mx - mx1) * (mx - mx1) + (my - my1) * (my - my1) + 1.0);
                mx = oEvent.clientX;
                my = oEvent.clientY;
                mx1 = oEvent1.clientX;
                my1 = oEvent1.clientY;
                l = Math.sqrt((mx - mx1) * (mx - mx1) + (my - my1) * (my - my1) + 1.0);
                len *= l1 / l;
            }
            lasttimen = n;
        },
        false);
    document.oncontextmenu = function (event) {
        if (mm == 1) {
            event.preventDefault();
        }
    };

    function draw() {
        date = new Date();
        var t2 = date.getTime();
        t1 = t2;
        gl.uniform1f(glx, cx * 2.0 / (cx + cy));
        gl.uniform1f(gly, cy * 2.0 / (cx + cy));
        gl.uniform1f(gllen, len);
        gl.uniform3f(glorigin, len * Math.cos(ang1) * Math.cos(ang2) + cenx, len * Math.sin(ang2) + ceny, len * Math.sin(ang1) * Math.cos(ang2) + cenz);
        gl.uniform3f(glright, Math.sin(ang1), 0, -Math.cos(ang1));
        gl.uniform3f(glup, -Math.sin(ang2) * Math.cos(ang1), Math.cos(ang2), -Math.sin(ang2) * Math.sin(ang1));
        gl.uniform3f(glforward, -Math.cos(ang1) * Math.cos(ang2), -Math.sin(ang2), -Math.sin(ang1) * Math.cos(ang2));
        gl.drawArrays(gl.TRIANGLES, 0, 6);
        gl.finish();
    }

    window.onresize = function () {
        cx = document.body.clientWidth;
        cy = document.body.clientHeight;
        if (cx > cy) {
            cx = cy;
        } else {
            cy = cx;
        }
        document.getElementById("shader-container").setAttribute("width", cx);
        document.getElementById("shader-container").setAttribute("height", cy);
        gl.viewport(0, 0, cx, cy);
    }

    window.onload = function () {
        cx = document.body.clientWidth;
        cy = document.body.clientHeight;
        if (cx > cy) {
            cx = cy;
        } else {
            cy = cx;
        }

        // Initialize canvas and WebGL context first
        canvas = document.getElementById('shader-container');
        canvas.setAttribute("width", cx);
        canvas.setAttribute("height", cy);
        gl = canvas.getContext('webgl');

        if (!gl) {
            showError('WebGL not supported in your browser');
            return;
        }

        // Initialize shaders and program
        var positions = [-1.0, -1.0, 0.0, 1.0, -1.0, 0.0, 1.0, 1.0, 0.0, -1.0, -1.0, 0.0, 1.0, 1.0, 0.0, -1.0, 1.0, 0.0];
        var VSHADER_SOURCE =
            "#version 100 \n" +
            "precision highp float;\n" +
            "attribute vec4 position;" +
            "varying vec3 dir, localdir;" +
            "uniform vec3 right, forward, up, origin;" +
            "uniform float x,y;" +
            "void main() {" +
            "   gl_Position = position; " +
            "   dir = forward + right * position.x*x + up * position.y*y;" +
            "   localdir.x = position.x*x;" +
            "   localdir.y = position.y*y;" +
            "   localdir.z = -1.0;" +
            "} ";
        var FSHADER_SOURCE =
            "#version 100 \n" +
            "#define PI 3.14159265358979324\n" +
            "#define M_L 0.3819660113\n" +
            "#define M_R 0.6180339887\n" +
            "#define MAXR 8\n" +
            "#define SOLVER 8\n" +
            "precision highp float;\n" +
            "float kernal(vec3 ver)\n;" +
            "uniform vec3 right, forward, up, origin;\n" +
            "varying vec3 dir, localdir;\n" +
            "uniform float len;\n" +
            "vec3 ver;\n" +
            "int sign;" +
            "float v, v1, v2;\n" +
            "float r1, r2, r3, r4, m1, m2, m3, m4;\n" +
            "vec3 n, reflect;\n" +
            "const float step = 0.002;\n" +
            "vec3 color;\n" +
            "void main() {\n" +
            "   color.r=0.0;\n" +
            "   color.g=0.0;\n" +
            "   color.b=0.0;\n" +
            "   sign=0;" +
            "   v1 = kernal(origin + dir * (step*len));\n" +
            "   v2 = kernal(origin);\n" +
            "   for (int k = 2; k < 1002; k++) {\n" +
            "      ver = origin + dir * (step*len*float(k));\n" +
            "      v = kernal(ver);\n" +
            "      if (v > 0.0 && v1 < 0.0) {\n" +
            "         r1 = step * len*float(k - 1);\n" +
            "         r2 = step * len*float(k);\n" +
            "         m1 = kernal(origin + dir * r1);\n" +
            "         m2 = kernal(origin + dir * r2);\n" +
            "         for (int l = 0; l < SOLVER; l++) {\n" +
            "            r3 = r1 * 0.5 + r2 * 0.5;\n" +
            "            m3 = kernal(origin + dir * r3);\n" +
            "            if (m3 > 0.0) {\n" +
            "               r2 = r3;\n" +
            "               m2 = m3;\n" +
            "            }\n" +
            "            else {\n" +
            "               r1 = r3;\n" +
            "               m1 = m3;\n" +
            "            }\n" +
            "         }\n" +
            "         if (r3 < 2.0 * len) {\n" +
            "               sign=1;" +
            "            break;\n" +
            "         }\n" +
            "      }\n" +
            "      if (v < v1&&v1>v2&&v1 < 0.0 && (v1*2.0 > v || v1 * 2.0 > v2)) {\n" +
            "         r1 = step * len*float(k - 2);\n" +
            "         r2 = step * len*(float(k) - 2.0 + 2.0*M_L);\n" +
            "         r3 = step * len*(float(k) - 2.0 + 2.0*M_R);\n" +
            "         r4 = step * len*float(k);\n" +
            "         m2 = kernal(origin + dir * r2);\n" +
            "         m3 = kernal(origin + dir * r3);\n" +
            "         for (int l = 0; l < MAXR; l++) {\n" +
            "            if (m2 > m3) {\n" +
            "               r4 = r3;\n" +
            "               r3 = r2;\n" +
            "               r2 = r4 * M_L + r1 * M_R;\n" +
            "               m3 = m2;\n" +
            "               m2 = kernal(origin + dir * r2);\n" +
            "            }\n" +
            "            else {\n" +
            "               r1 = r2;\n" +
            "               r2 = r3;\n" +
            "               r3 = r4 * M_R + r1 * M_L;\n" +
            "               m2 = m3;\n" +
            "               m3 = kernal(origin + dir * r3);\n" +
            "            }\n" +
            "         }\n" +
            "         if (m2 > 0.0) {\n" +
            "            r1 = step * len*float(k - 2);\n" +
            "            r2 = r2;\n" +
            "            m1 = kernal(origin + dir * r1);\n" +
            "            m2 = kernal(origin + dir * r2);\n" +
            "            for (int l = 0; l < SOLVER; l++) {\n" +
            "               r3 = r1 * 0.5 + r2 * 0.5;\n" +
            "               m3 = kernal(origin + dir * r3);\n" +
            "               if (m3 > 0.0) {\n" +
            "                  r2 = r3;\n" +
            "                  m2 = m3;\n" +
            "               }\n" +
            "               else {\n" +
            "                  r1 = r3;\n" +
            "                  m1 = m3;\n" +
            "               }\n" +
            "            }\n" +
            "            if (r3 < 2.0 * len&&r3> step*len) {\n" +
            "                   sign=1;" +
            "               break;\n" +
            "            }\n" +
            "         }\n" +
            "         else if (m3 > 0.0) {\n" +
            "            r1 = step * len*float(k - 2);\n" +
            "            r2 = r3;\n" +
            "            m1 = kernal(origin + dir * r1);\n" +
            "            m2 = kernal(origin + dir * r2);\n" +
            "            for (int l = 0; l < SOLVER; l++) {\n" +
            "               r3 = r1 * 0.5 + r2 * 0.5;\n" +
            "               m3 = kernal(origin + dir * r3);\n" +
            "               if (m3 > 0.0) {\n" +
            "                  r2 = r3;\n" +
            "                  m2 = m3;\n" +
            "               }\n" +
            "               else {\n" +
            "                  r1 = r3;\n" +
            "                  m1 = m3;\n" +
            "               }\n" +
            "            }\n" +
            "            if (r3 < 2.0 * len&&r3> step*len) {\n" +
            "                   sign=1;" +
            "               break;\n" +
            "            }\n" +
            "         }\n" +
            "      }\n" +
            "      v2 = v1;\n" +
            "      v1 = v;\n" +
            "   }\n" +
            "   if (sign==1) {\n" +
            "      ver = origin + dir*r3 ;\n" +
            "       r1=ver.x*ver.x+ver.y*ver.y+ver.z*ver.z;" +
            "      n.x = kernal(ver - right * (r3*0.00025)) - kernal(ver + right * (r3*0.00025));\n" +
            "      n.y = kernal(ver - up * (r3*0.00025)) - kernal(ver + up * (r3*0.00025));\n" +
            "      n.z = kernal(ver + forward * (r3*0.00025)) - kernal(ver - forward * (r3*0.00025));\n" +
            "      r3 = n.x*n.x+n.y*n.y+n.z*n.z;\n" +
            "      n = n * (1.0 / sqrt(r3));\n" +
            "      ver = localdir;\n" +
            "      r3 = ver.x*ver.x+ver.y*ver.y+ver.z*ver.z;\n" +
            "      ver = ver * (1.0 / sqrt(r3));\n" +
            "      reflect = n * (-2.0*dot(ver, n)) + ver;\n" +
            "      r3 = reflect.x*0.276+reflect.y*0.920+reflect.z*0.276;\n" +
            "      r4 = n.x*0.276+n.y*0.920+n.z*0.276;\n" +
            "      r3 = max(0.0,r3);\n" +
            "      r3 = r3 * r3*r3*r3;\n" +
            "      r3 = r3 * 0.45 + r4 * 0.25 + 0.3;\n" +
            "      n.x = sin(r1*10.0)*0.5+0.5;\n" +
            "      n.y = sin(r1*10.0+2.05)*0.5+0.5;\n" +
            "      n.z = sin(r1*10.0-2.05)*0.5+0.5;\n" +
            "      color = n*r3;\n" +
            "   }\n" +
            "   gl_FragColor = vec4(color.x, color.y, color.z, 1.0);" +
            "}";
        vertshader = gl.createShader(gl.VERTEX_SHADER);
        fragshader = gl.createShader(gl.FRAGMENT_SHADER);
        shaderProgram = gl.createProgram();
        gl.shaderSource(vertshader, VSHADER_SOURCE);
        gl.compileShader(vertshader);
        var infov = gl.getShaderInfoLog(vertshader);
        gl.shaderSource(fragshader, FSHADER_SOURCE + KERNEL);
        gl.compileShader(fragshader);
        var infof = gl.getShaderInfoLog(fragshader);
        gl.attachShader(shaderProgram, vertshader);
        gl.attachShader(shaderProgram, fragshader);
        gl.linkProgram(shaderProgram);
        gl.useProgram(shaderProgram);
        if (!gl.getProgramParameter(shaderProgram, gl.LINK_STATUS)) {
            var info = gl.getProgramInfoLog(shaderProgram);
            throw 'Could not compile WebGL program. \n\n' + infov + infof + info;
        }
        glposition = gl.getAttribLocation(shaderProgram, 'position');
        glright = gl.getUniformLocation(shaderProgram, 'right');
        glforward = gl.getUniformLocation(shaderProgram, 'forward');
        glup = gl.getUniformLocation(shaderProgram, 'up');
        glorigin = gl.getUniformLocation(shaderProgram, 'origin');
        glx = gl.getUniformLocation(shaderProgram, 'x');
        gly = gl.getUniformLocation(shaderProgram, 'y');
        gllen = gl.getUniformLocation(shaderProgram, 'len');
        var buffer = gl.createBuffer();
        gl.bindBuffer(gl.ARRAY_BUFFER, buffer);
        gl.bufferData(gl.ARRAY_BUFFER, new Float32Array(positions), gl.STATIC_DRAW);
        gl.vertexAttribPointer(glposition, 3, gl.FLOAT, false, 0, 0);
        gl.enableVertexAttribArray(glposition);

        draw();
        window.requestAnimationFrame(ontimer);
        document.getElementById("kernel").value = KERNEL;

        gl.viewport(0, 0, cx, cy);

        document.getElementById("btn").addEventListener("click", function () {
            const configPanel = document.getElementById("config");
            const isHidden = configPanel.classList.contains("hidden");
            this.innerText = isHidden ? "Hide" : "Config";
            configPanel.classList.toggle("hidden");
        });

        // Apply button functionality
        document.getElementById("apply").addEventListener("click", function () {
            showLoading();
            try {
                KERNEL = document.getElementById("kernel").value;

                gl.shaderSource(fragshader, FSHADER_SOURCE + KERNEL);
                gl.compileShader(fragshader);
                const infof = gl.getShaderInfoLog(fragshader);

                gl.linkProgram(shaderProgram);
                gl.useProgram(shaderProgram);

                if (!gl.getProgramParameter(shaderProgram, gl.LINK_STATUS)) {
                    throw new Error(infof + gl.getProgramInfoLog(shaderProgram));
                }

                glposition = gl.getAttribLocation(shaderProgram, 'position');
                glright = gl.getUniformLocation(shaderProgram, 'right');
                glforward = gl.getUniformLocation(shaderProgram, 'forward');
                glup = gl.getUniformLocation(shaderProgram, 'up');
                glorigin = gl.getUniformLocation(shaderProgram, 'origin');
                glx = gl.getUniformLocation(shaderProgram, 'x');
                gly = gl.getUniformLocation(shaderProgram, 'y');
                gllen = gl.getUniformLocation(shaderProgram, 'len');

                showError('Shader applied successfully!');
            } catch (error) {
                showError('Error applying shader: ' + error.message);
            } finally {
                hideLoading();
            }
        });

        document.getElementById("undo").addEventListener("click", function () {
            document.getElementById("kernel").value = KERNEL;
        });

        const modal = document.getElementById('modal');
        const infoButton = document.getElementById('infoButton');
        const closeModal = document.getElementById('closeModal');

        function closeModalHandler() {
            modal.classList.add('hidden');
            document.body.style.overflow = 'auto';
        }

        function openModalHandler() {
            modal.classList.remove('hidden');
            document.body.style.overflow = 'hidden';
        }

        infoButton.addEventListener('click', openModalHandler);
        closeModal.addEventListener('click', closeModalHandler);

        modal.addEventListener('click', (e) => {
            if (e.target === modal) {
                closeModalHandler();
            }
        });

        document.addEventListener('keydown', (e) => {
            if (e.key === 'Escape' && !modal.classList.contains('hidden')) {
                closeModalHandler();
            }
        });
    }
</script><script defer src="https://static.cloudflareinsights.com/beacon.min.js/vcd15cbe7772f49c399c6a5babf22c1241717689176015" integrity="sha512-ZpsOmlRQV6y907TI0dKBHq9Md29nnaEIPlkf84rnaERnq6zvWvPUqr2ft8M1aS28oN72PdrCzSjY4U6VaAw1EQ==" data-cf-beacon='{"rayId":"96bca6573b3b3a90","version":"2025.7.0","r":1,"serverTiming":{"name":{"cfExtPri":true,"cfEdge":true,"cfOrigin":true,"cfL4":true,"cfSpeedBrain":true,"cfCacheStatus":true}},"token":"bd43be95c41947a1af3093ec42721174","b":1}' crossorigin="anonymous"></script>
</body>
</html>